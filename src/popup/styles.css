/* Theme Variables */
:root {
  /* Primary Colors - Modern Tech Theme */
  --color-primary: #4cc9f0;
  --color-primary-hover: #3db8df;
  --color-primary-light: rgba(76, 201, 240, 0.15);
  --color-primary-dark: #2a9cc0;
  --color-accent-1: #7209b7;
  /* Purple accent */
  --color-accent-2: #4ecc8f;
  /* Green accent */

  /* Text Colors */
  --text-primary: #f0f0f0;
  --text-secondary: #b0b0b0;
  --text-placeholder: #808080;

  /* Background Colors */
  --bg-dark: #0a1929;
  --bg-light: #112240;
  --bg-white: #0d1b2a;
  --border-color: rgba(76, 201, 240, 0.2);
  --border-color-light: rgba(76, 201, 240, 0.1);

  /* Glassmorphism - Enhanced */
  --glass-bg: rgba(13, 27, 42, 0.65);
  --glass-blur: 20px;
  --glass-border: 1px solid rgba(76, 201, 240, 0.15);
  --glass-glow: 0 0 20px rgba(76, 201, 240, 0.05);

  /* Gradients */
  --gradient-bg: linear-gradient(135deg, #0a1929 0%, #112240 100%);
  --gradient-accent: linear-gradient(135deg, #4cc9f0 0%, #3a86ff 100%);
  --gradient-accent-alt: linear-gradient(135deg, #7209b7 0%, #3a0ca3 100%);

  /* Shadows */
  --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.25);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.35);
  --shadow-lg: 0 8px 28px rgba(0, 0, 0, 0.45);
  --shadow-glow: 0 0 15px rgba(76, 201, 240, 0.15);
}

/* Base styles */
/* Google Fonts Preconnect */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Roboto:wght@400;500;700&family=Source+Sans+Pro:wght@400;600&display=swap');

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Source Sans Pro', 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Custom scrollbar for elements that need it */
::-webkit-scrollbar {
  width: 6px;
  /* More compact width */
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(10, 25, 41, 0.4);
  /* Darker, more subtle track */
  border-radius: 6px;
  margin: 4px 0;
  /* Add some margin for a floating effect */
}

@keyframes scrollbar-glow {
  0% {
    box-shadow: inset 0 0 3px rgba(76, 201, 240, 0.2);
  }

  50% {
    box-shadow: inset 0 0 5px rgba(76, 201, 240, 0.4);
  }

  100% {
    box-shadow: inset 0 0 3px rgba(76, 201, 240, 0.2);
  }
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #112240, #0a1929);
  /* Dark blue gradient */
  border-radius: 6px;
  border: 1px solid rgba(76, 201, 240, 0.2);
  /* Subtle border glow */
  box-shadow: inset 0 0 3px rgba(76, 201, 240, 0.3);
  /* Inner glow effect */
  animation: scrollbar-glow 3s infinite ease-in-out;
  /* Subtle animation */
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #1a365d, #112240);
  /* Lighter on hover */
  border-color: rgba(76, 201, 240, 0.4);
  /* More visible border on hover */
  box-shadow: inset 0 0 5px rgba(76, 201, 240, 0.5);
  /* Enhanced glow on hover */
}

/* Hide scrollbar buttons */
::-webkit-scrollbar-button {
  display: none;
}

/* Style the corner where scrollbars meet */
::-webkit-scrollbar-corner {
  background: rgba(10, 25, 41, 0.4);
}

body {
  width: 700px;
  background: var(--gradient-bg);
  color: var(--text-primary);
  overflow: hidden;
  scrollbar-width: none;
  /* For Firefox */
  -ms-overflow-style: none;
  /* For Internet Explorer and Edge */
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
}

body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at top right, rgba(76, 201, 240, 0.15), transparent 50%),
    radial-gradient(circle at bottom left, rgba(114, 9, 183, 0.1), transparent 50%),
    radial-gradient(circle at center, rgba(58, 134, 255, 0.05), transparent 60%);
  z-index: -1;
  animation: subtle-pulse 8s ease-in-out infinite alternate;
}

@keyframes subtle-pulse {
  0% {
    opacity: 0.8;
  }

  100% {
    opacity: 1;
  }
}

body::-webkit-scrollbar {
  display: none;
  /* For Chrome, Safari and Opera */
}

.container {
  padding: 24px;
  background-color: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: var(--glass-border);
  border-radius: 20px;
  box-shadow: var(--shadow-md), var(--shadow-glow);
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 10px;
  max-height: 400px;
  /* Limit height and enable scrolling if needed */
  scrollbar-width: thin;
  /* For Firefox */
  scrollbar-color: var(--color-primary-dark) rgba(10, 25, 41, 0.4);
  /* For Firefox */
}

.translate-section,
.vi-en-section {
  background: rgba(13, 27, 42, 0.75);
  border: none;
  border-radius: 16px;
  padding: 20px;
  margin: 15px 0;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.translate-section:hover,
.vi-en-section:hover {
  border-color: rgba(76, 201, 240, 0.4);
  box-shadow: 0 8px 32px rgba(76, 201, 240, 0.1);
  transform: translateY(-2px);
}

.translate-section textarea,
.vi-en-section textarea,
.translate-section .textarea-styled-output,
.vi-en-section .textarea-styled-output {
  width: 100%;
  background: rgba(13, 27, 42, 0.5);
  border: 1px solid rgba(76, 201, 240, 0.15);
  border-radius: 12px;
  color: var(--text-primary);
  padding: 15px;
  margin: 10px 0;
  font-size: 15px;
  line-height: 1.6;
  resize: vertical;
  transition: all 0.3s ease;
  /* Ensure scrollbar is visible but elegant */
  scrollbar-width: thin;
  /* For Firefox */
  scrollbar-color: var(--color-primary-dark) rgba(10, 25, 41, 0.4);
  /* For Firefox */
}

/* Add focus indicator for better accessibility */
.translate-section textarea:focus,
.vi-en-section textarea:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(76, 201, 240, 0.2), 0 0 8px rgba(76, 201, 240, 0.1);
  outline: none;
}

/* Add subtle placeholder styling */
.translate-section textarea::placeholder,
.vi-en-section textarea::placeholder {
  color: rgba(176, 176, 176, 0.5);
  font-style: italic;
  transition: opacity 0.2s ease;
}

.translate-section textarea:focus::placeholder,
.vi-en-section textarea:focus::placeholder {
  opacity: 0.3;
}

/* Removed duplicate focus styles */

.textarea-styled-output {
  position: relative;
  min-height: 30px;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}

.textarea-styled-output::before {
  content: attr(placeholder);
  position: absolute;
  top: 15px;
  left: 15px;
  color: var(--text-placeholder);
  pointer-events: none;
  opacity: 0.6;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(76, 201, 240, 0.3), transparent);
}

.container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at top right, rgba(76, 201, 240, 0.08), transparent 30%),
    radial-gradient(circle at bottom left, rgba(114, 9, 183, 0.05), transparent 30%);
  pointer-events: none;
  z-index: 0;
}

.header {
  margin-bottom: 24px;
  border-bottom: 1px solid var(--border-color-light);
  padding-bottom: 18px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 120px;
  height: 2px;
  background: var(--gradient-accent);
  border-radius: 2px;
  box-shadow: 0 0 8px rgba(76, 201, 240, 0.5);
  animation: glow-pulse 3s infinite alternate;
}

@keyframes glow-pulse {
  0% {
    opacity: 0.7;
    box-shadow: 0 0 8px rgba(76, 201, 240, 0.3);
  }

  100% {
    opacity: 1;
    box-shadow: 0 0 12px rgba(76, 201, 240, 0.6);
  }
}

h1 {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 12px rgba(76, 201, 240, 0.4);
  position: relative;
  display: inline-block;
}

/* Enhanced collapsible sections */
.primary-settings-collapse {
  margin-bottom: 16px;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(76, 201, 240, 0.1);
}

.primary-settings-collapse summary {
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.2s ease;
  padding: 12px 16px;
  background-color: rgba(17, 34, 64, 0.6);
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  min-height: 42px;
  text-align: center;
}

.primary-settings-collapse .summary-text {
  position: relative;
  z-index: 1;
}

.primary-settings-collapse summary::after {
  content: '▼';
  font-size: 0.7rem;
  color: var(--color-primary);
  transition: transform 0.3s ease;
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.primary-settings-collapse[open] summary::after {
  transform: translateY(-50%) rotate(180deg);
}

.primary-settings-collapse summary:hover {
  color: var(--color-primary);
  background-color: rgba(17, 34, 64, 0.8);
}

.primary-settings-collapse .collapse-content {
  padding: 16px;
  background-color: rgba(10, 25, 41, 0.4);
}

.api-key-container {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}



.prompt-input button:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.prompt-input button:active {
  transform: translateY(0);
  box-shadow: none;
}

#apiKey {
  flex: 1;
  padding: 12px 14px;
  background-color: rgba(10, 25, 41, 0.6);
  border: none;
  font-size: 14px;
  color: var(--text-primary);
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15);
  letter-spacing: 0.3px;
}

#apiKey:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(76, 201, 240, 0.2), inset 0 2px 4px rgba(0, 0, 0, 0.15);
  background-color: rgba(10, 25, 41, 0.8);
}

.button {
  background: var(--gradient-accent);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 18px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.25s ease;
  box-shadow: 0 2px 10px rgba(76, 201, 240, 0.3), 0 0 15px rgba(76, 201, 240, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  letter-spacing: 0.3px;
  /* Add focus state for keyboard navigation */
  outline: none;
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.4s ease;
}

.button:hover {
  background: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(76, 201, 240, 0.4), 0 0 20px rgba(76, 201, 240, 0.2);
}

.button:hover::before {
  left: 100%;
}

.button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(76, 201, 240, 0.3), 0 0 10px rgba(76, 201, 240, 0.1);
}

.button:focus-visible {
  box-shadow: 0 0 0 3px rgba(76, 201, 240, 0.4), 0 2px 10px rgba(76, 201, 240, 0.3);
  outline: none;
}

.settings {
  margin-bottom: 20px;
  padding: 18px;
  background-color: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: none;
  border-radius: 16px;
  box-shadow: var(--shadow-md), var(--shadow-glow);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.settings:hover {
  box-shadow: var(--shadow-md), 0 0 20px rgba(76, 201, 240, 0.2);
  border-color: rgba(76, 201, 240, 0.25);
}

.settings::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(76, 201, 240, 0.08), transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.setting-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-group input[type="checkbox"] {
  accent-color: var(--color-primary);
  width: 18px;
  height: 18px;
  margin-right: 8px;
}

.setting-group select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
  color: var(--text-primary);
  background-color: rgba(13, 17, 23, 0.6);
  min-width: 140px;
  margin-left: 8px;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  appearance: none;
  background-image: url("../../assets/icons/dropdown-arrow.svg");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 32px;
}

.setting-group select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 194, 203, 0.2), inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.setting-group label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group label {
  min-width: 80px;
  font-size: 13px;
  color: var(--text-secondary);
}

.setting-group select {
  flex: 1;
  padding: 6px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 13px;
  color: var(--text-primary);
  background-color: var(--bg-white);
}

.setting-group .font-size-input {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 0;
  position: relative;
  background-color: rgba(13, 27, 42, 0.3);
  border-radius: 6px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 120px;
}

.setting-group input[type="number"] {
  width: 40px;
  padding: 4px 0;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
  background-color: transparent;
  text-align: center;
  -moz-appearance: textfield;
  appearance: textfield;
  box-shadow: none;
  outline: none;
}

.setting-group input[type="number"]::-webkit-inner-spin-button,
.setting-group input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.setting-group .unit {
  color: var(--color-primary);
  font-size: 12px;
  font-weight: 500;
  margin-left: 6px;
  opacity: 0.8;
}

.font-size-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(76, 201, 240, 0.08);
  border: 1px solid rgba(76, 201, 240, 0.15);
  color: rgba(76, 201, 240, 0.85);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0;
  line-height: 1;
}

.font-size-btn.decrease {
  border-radius: 4px 0 0 4px;
}

.font-size-btn.increase {
  border-radius: 0 4px 4px 0;
}

.font-size-btn:hover {
  background: rgba(76, 201, 240, 0.15);
  border-color: rgba(76, 201, 240, 0.25);
  color: rgba(76, 201, 240, 1);
  box-shadow: 0 0 4px rgba(76, 201, 240, 0.2);
  transform: translateY(-1px);
}

.font-size-btn:active {
  transform: translateY(0) scale(0.98);
  background: rgba(76, 201, 240, 0.2);
  transition: all 0.1s ease;
}

.prompt-input {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: none;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.prompt-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at bottom right, rgba(0, 119, 182, 0.08), transparent 70%);
  pointer-events: none;
}


.prompt-input label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.prompt-input textarea.auto-save {
  width: 100%;
  padding: 14px;
  border: none;
  border-radius: 10px;
  font-size: 14px !important;
  line-height: 1.6;
  resize: none;
  min-height: 40px;
  max-height: none;
  background-color: rgba(10, 25, 41, 0.5);
  color: var(--text-primary);
  transition: all 0.3s ease;
  margin-top: 10px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.2px;
  overflow: hidden;
}

@keyframes flash {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.prompt-input textarea.auto-save:focus {
  background-color: rgba(10, 25, 41, 0.7);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.25), 0 0 0 2px rgba(76, 201, 240, 0.15);
  outline: none;
  animation: flash 0.5s ease-in-out;
}

.prompt-input textarea:focus {
  outline: none;
  box-shadow: none;
}

.prompt-input textarea::placeholder {
  color: var(--text-placeholder);
}

.prompt-input button {
  align-self: flex-end;
  background-color: var(--color-primary);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.prompt-input button:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.prompt-input button:active {
  transform: translateY(0);
  box-shadow: none;
}

.language-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

label {
  font-size: 14px;
  color: var(--text-secondary);
}

select {
  flex: 1;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-white);
  font-size: 14px;
}

.info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border-radius: 12px;
  border: var(--glass-border);
  box-shadow: var(--shadow-sm), var(--shadow-glow);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.info:hover {
  box-shadow: var(--shadow-sm), 0 0 15px rgba(76, 201, 240, 0.15);
  border-color: rgba(76, 201, 240, 0.25);
}

.info::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--gradient-accent);
  box-shadow: 0 0 8px rgba(76, 201, 240, 0.5);
}

.info p {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 12px;
  position: relative;
  z-index: 1;
  letter-spacing: 0.2px;
  line-height: 1.5;
  padding-left: 12px;
}

.info p:last-child {
  margin-bottom: 0;
}

.note {
  font-size: 13px;
  color: var(--text-secondary);
  font-style: italic;
  opacity: 0.9;
  position: relative;
}

.note::before {
  content: '💡';
  position: absolute;
  left: -12px;
  opacity: 0.8;
}

.footer {
  font-size: 13px;
  color: var(--text-secondary);
  text-align: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color-light);
  position: relative;
}

.footer p {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  font-weight: 500;
  letter-spacing: 0.3px;
  opacity: 0.9;
}

.footer::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 1px;
  background: var(--gradient-accent);
  opacity: 0.5;
}

/* Collapsible primary settings */
.primary-settings-collapse {
  margin-bottom: 22px;
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  background-color: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  box-shadow: var(--shadow-md), var(--shadow-glow);
  position: relative;
  transition: all 0.3s ease;
}

.primary-settings-collapse:hover {
  box-shadow: var(--shadow-md), 0 0 20px rgba(76, 201, 240, 0.2);
  border-color: rgba(76, 201, 240, 0.25);
}

.primary-settings-collapse::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top left, rgba(76, 201, 240, 0.08), transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.primary-settings-collapse summary {
  padding: 12px 20px;
  background-color: rgba(13, 27, 42, 0.7);
  color: var(--color-primary);
  font-weight: 600;
  cursor: pointer;
  list-style: none;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  letter-spacing: 0.3px;
}

.primary-settings-collapse summary:hover {
  background-color: rgba(76, 201, 240, 0.1);
}

.primary-settings-collapse summary::-webkit-details-marker {
  display: none;
}

.primary-settings-collapse summary::after {
  content: "+";
  transition: all 0.3s ease;
  color: var(--color-primary);
  font-size: 20px;
  line-height: 1;
  margin-left: 8px;
}

.primary-settings-collapse[open] summary::after {
  content: "−";
  line-height: 0.5;
  vertical-align: middle;
}

.primary-settings-collapse[open] .collapse-content {
  padding: 18px 20px;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.primary-settings-collapse[open] {
  box-shadow: var(--shadow-md), 0 0 25px rgba(76, 201, 240, 0.15);
}

.primary-settings-collapse .api-key-container {
  margin: 0;
}

.primary-settings-collapse .prompt-input {
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}

/* START: New Translation Section Styles */
.translate-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 14px;
  position: relative;
  z-index: 1;
}

.translate-section label {
  min-width: auto;
  margin-bottom: 6px;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  letter-spacing: 0.2px;
}

.translate-section label.output-label {
  margin-top: 10px;
  color: var(--color-primary);
}

.translate-card {
  display: flex;
  flex-direction: column;
  border-top: 1px solid var(--border-color-light);
  border-radius: 0.5rem;
  padding: 0.75rem;
  text-align: start;
  max-width: 280px;
  background: linear-gradient(180deg, var(--glass-bg) 50%, rgba(13, 27, 42, 0.1) 100%);
  transition: background 0.3s ease;
}

.translate-card:hover {
  background: linear-gradient(180deg, rgba(13, 27, 42, 0.75) 50%, rgba(13, 27, 42, 0.2) 100%);
}

.translate-card .header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.translate-card .header h3 {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1;
  color: var(--text-primary);
}

.translate-card p {
  margin-top: 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.translate-section textarea {
  width: 100%;
  padding: 12px 14px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: transparent;
  resize: vertical;
  min-height: 70px;
  transition: all 0.3s ease;
  box-shadow: none;
  letter-spacing: 0.2px;
}

/* Remove border for translation input textarea */
#translate-input,
#translate-input:focus {
  border: none !important;
  box-shadow: none;
  background-color: rgba(10, 25, 41, 0.4);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.translate-section textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light), inset 0 2px 4px rgba(0, 0, 0, 0.2);
  background-color: rgba(10, 25, 41, 0.7);
}

.translate-section textarea::placeholder {
  color: var(--text-placeholder);
  opacity: 0.8;
}

/* Styles for the new div acting as output area */
.textarea-styled-output {
  width: 100%;
  padding: 14px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: transparent;
  min-height: 90px;
  height: auto;
  word-break: break-word;
  cursor: default;
  position: relative;
  box-shadow: none;
  transition: all 0.3s ease;
  letter-spacing: 0.2px;
}

/* Markdown content container to prevent style conflicts */
.markdown-content {
  width: 100%;
  color: var(--text-primary);
  line-height: 1;
  font-size: inherit;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Markdown specific styling within the container */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: var(--text-primary);
  margin: 0.6em 0 0.2em 0;
  font-weight: 600;
  line-height: 1.3;
}

.markdown-content h1 {
  font-size: 1.4em;
}

.markdown-content h2 {
  font-size: 1.3em;
}

.markdown-content h3 {
  font-size: 1.2em;
}

.markdown-content h4 {
  font-size: 1.1em;
}

.markdown-content h5 {
  font-size: 1.05em;
}

.markdown-content h6 {
  font-size: 1em;
}

.markdown-content p {
  margin: 0 0;
  margin-block-start: -0.1em;
  margin-block-end: -0.1em;
  color: var(--text-primary);
  line-height: 1.5;
}

.markdown-content ul,
.markdown-content ol {
  margin: 0 0;
  margin-block-start: 0;
  margin-block-end: 0;
  padding-left: 1.5em;
  color: var(--text-primary);
  line-height: 0.4;
}

.markdown-content li {
  margin: 0 0;
  line-height: 1.4;
}

.markdown-content blockquote {
  margin: 0.5em 0;
  padding: 0.5em 0.8em;
  border-left: 3px solid var(--color-primary);
  background-color: rgba(76, 201, 240, 0.05);
  border-radius: 0 6px 6px 0;
  color: var(--text-primary);
  font-style: italic;
}

.markdown-content code {
  background-color: rgba(255, 255, 255, 0.08);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Consolas, monospace;
  font-size: 0.9em;
  color: #94a3b8;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.markdown-content pre {
  background-color: rgba(255, 255, 255, 0.05);
  padding: 1em;
  border-radius: 8px;
  overflow-x: auto;
  margin: 0.8em 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.markdown-content pre code {
  background: none;
  padding: 0;
  border: none;
  font-size: 0.85em;
  color: #94a3b8;
}

.markdown-content strong,
.markdown-content b {
  font-weight: 600;
  color: var(--text-primary);
}

.markdown-content em,
.markdown-content i {
  font-style: italic;
  color: var(--text-primary);
}

.markdown-content a {
  color: var(--color-primary);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.markdown-content a:hover {
  border-bottom-color: var(--color-primary);
}

.markdown-content hr {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  margin: 1.5em 0;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 0.8em 0;
  font-size: 0.9em;
}

.markdown-content th,
.markdown-content td {
  padding: 0.5em 0.8em;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: left;
}

.markdown-content th {
  background-color: rgba(255, 255, 255, 0.05);
  font-weight: 600;
  color: var(--text-primary);
}

.markdown-content td {
  color: var(--text-primary);
}

/* Placeholder simulation for the div */
.textarea-styled-output:empty::before {
  content: attr(placeholder);
  color: var(--text-placeholder);
  position: absolute;
  left: 14px;
  top: 14px;
  pointer-events: none;
  opacity: 0.8;
  font-style: italic;
  letter-spacing: 0.2px;
  display: block;
}

.textarea-styled-output:not(:empty)::before {
  display: none;
}

/* Remove duplicate rule block */

/* Specific ID styles if needed (e.g., padding for copy button) */
#en-output {
  padding-right: 35px;
  /* Keep padding for copy button */
}

/* END: New Translation Section Styles */

/* START: Vi-En Translation Section & Custom Prompt Styles */

/* Style for the new custom prompt textarea */

/* Style for the Vi-En section container */
.vi-en-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 14px;
  position: relative;
  z-index: 1;
}

/* Style for labels within the Vi-En section */
.vi-en-section label {
  min-width: auto;
  margin-bottom: 6px;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  letter-spacing: 0.2px;
}

.vi-en-section label.output-label {
  margin-top: 10px;
  text-align: center;
  color: var(--color-primary);
}

/* Style for textareas within the Vi-En section */
.vi-en-section textarea {
  width: 100%;
  padding: 12px 14px;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: rgba(10, 25, 41, 0.5);
  resize: vertical;
  min-height: 70px;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.2px;
}

/* Remove border for Vi-En input textarea */
#vi-input,
#vi-input:focus {
  border: none !important;
  box-shadow: none;
  background-color: rgba(10, 25, 41, 0.4);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.vi-en-section textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light), inset 0 2px 4px rgba(0, 0, 0, 0.2);
  background-color: rgba(10, 25, 41, 0.7);
}

.vi-en-section textarea::placeholder {
  color: var(--text-placeholder);
  /* Match .translate-section textarea::placeholder */
}

/* Container for the output textarea and copy button */
.vi-en-section .output-container {
  position: relative;
  /* Needed for absolute positioning of the button */
  width: 100%;
}

/* Style for the copy button inside the output container */
.vi-en-section .output-container .copy-button {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 8px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.3s ease;
  outline: none;
}

.vi-en-section .output-container .copy-button:hover {
  transform: translateY(-1px);
  background-color: rgba(76, 201, 240, 0.1);
  box-shadow: 0 0 8px rgba(76, 201, 240, 0.2);
}

.vi-en-section .output-container .copy-button:active {
  transform: translateY(0);
  background-color: rgba(76, 201, 240, 0.2);
}

.vi-en-section .output-container .copy-button svg {
  width: 24px;
  height: 24px;
  fill: #FFFFFF;
  transition: all 0.3s ease;
}

.vi-en-section .output-container .copy-button:hover svg {
  fill: #FFFFFF;
  transform: scale(1.1);
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.7));
}

/* END: Vi-En Translation Section & Custom Prompt Styles */

/* Font family and size classes for dynamic styling */
/* System Default */
.font-system {
  font-family: -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
}

/* Google Fonts */
.font-baloo {
  font-family: 'Baloo 2', cursive;
}

.font-be-vietnam {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.font-cabin {
  font-family: 'Cabin', sans-serif;
}

.font-comfortaa {
  font-family: 'Comfortaa', cursive;
}

.font-crimson-pro {
  font-family: 'Crimson Pro', serif;
}

.font-crimson-text {
  font-family: 'Crimson Text', serif;
}

.font-fira {
  font-family: 'Fira Sans', sans-serif;
}

.font-fraunces {
  font-family: 'Fraunces', serif;
}

.font-geologica {
  font-family: 'Geologica', sans-serif;
}

.font-lexend {
  font-family: 'Lexend Deca', sans-serif;
}

.font-literata {
  font-family: 'Literata', serif;
}

.font-mali {
  font-family: 'Mali', cursive;
}

.font-manrope {
  font-family: 'Manrope', sans-serif;
}

.font-maven {
  font-family: 'Maven Pro', sans-serif;
}

.font-montserrat {
  font-family: 'Montserrat', sans-serif;
}

.font-mulish {
  font-family: 'Mulish', sans-serif;
}

.font-newsreader {
  font-family: 'Newsreader', serif;
}

.font-noto {
  font-family: 'Noto Sans', sans-serif;
}

.font-nunito-sans {
  font-family: 'Nunito Sans', sans-serif;
}

.font-nunito {
  font-family: 'Nunito', sans-serif;
}

.font-open-sans {
  font-family: 'Open Sans', sans-serif;
}

.font-petrona {
  font-family: 'Petrona', serif;
}

.font-questrial {
  font-family: 'Questrial', sans-serif;
}

.font-quicksand {
  font-family: 'Quicksand', sans-serif;
}

.font-raleway {
  font-family: 'Raleway', sans-serif;
}

.font-roboto-mono {
  font-family: 'Roboto Mono', monospace;
}

.font-roboto {
  font-family: 'Roboto', sans-serif;
}

.font-space-grotesk {
  font-family: 'Space Grotesk', sans-serif;
}

.font-varela {
  font-family: 'Varela Round', sans-serif;
}

.font-work-sans {
  font-family: 'Work Sans', sans-serif;
}

/* Font size classes */
.font-size-8 {
  font-size: 8px;
}

.font-size-9 {
  font-size: 9px;
}

.font-size-10 {
  font-size: 10px;
}

.font-size-11 {
  font-size: 11px;
}

.font-size-12 {
  font-size: 12px;
}

.font-size-13 {
  font-size: 13px;
}

.font-size-14 {
  font-size: 14px;
}

.font-size-15 {
  font-size: 15px;
}

.font-size-16 {
  font-size: 16px;
}

.font-size-17 {
  font-size: 17px;
}

.font-size-18 {
  font-size: 18px;
}

.font-size-19 {
  font-size: 19px;
}

.font-size-20 {
  font-size: 20px;
}

.font-size-21 {
  font-size: 21px;
}

.font-size-22 {
  font-size: 22px;
}

.font-size-23 {
  font-size: 23px;
}

.font-size-24 {
  font-size: 24px;
}

.font-size-25 {
  font-size: 25px;
}

.font-size-26 {
  font-size: 26px;
}

.font-size-27 {
  font-size: 27px;
}

.font-size-28 {
  font-size: 28px;
}

.font-size-29 {
  font-size: 29px;
}

.font-size-30 {
  font-size: 30px;
}

.font-size-31 {
  font-size: 31px;
}

.font-size-32 {
  font-size: 32px;
}

/* Loading animation styles */
.loading-animation {
  position: absolute;
  z-index: 9999;
  background: var(--glass-bg);
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: var(--shadow-md), var(--shadow-glow);
  display: flex;
  align-items: center;
  gap: 10px;
  border: var(--glass-border);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
}

.loading-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(76, 201, 240, 0.2);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s linear infinite;
  box-shadow: 0 0 10px rgba(76, 201, 240, 0.2);
}

.loading-text {
  font-size: 14px;
  color: var(--text-primary);
  letter-spacing: 0.2px;
  font-weight: 500;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Button loading state */
.gemini-translator-button.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Utility class for flex row with space between items */
.flex-row-space-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Styles for pre/code blocks within output divs */
.textarea-styled-output pre {
  white-space: pre-wrap;
  /* Allow wrapping, preserve line breaks */
  word-break: break-word;
  /* Break long words/lines */
  background-color: rgba(0, 0, 0, 0.05);
  /* Subtle background for code blocks */
  padding: 8px;
  /* Padding inside code blocks */
  border-radius: 4px;
  /* Rounded corners */
  margin: 0.5em 0;
  /* Margin around code blocks */
  overflow-x: auto;
  /* Add horizontal scroll if needed */
}

.textarea-styled-output code {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  /* Monospace font */
  font-size: 0.9em;
  /* Slightly smaller font size for code */
  background-color: transparent;
  /* Ensure code background doesn't override pre */
  padding: 0.1em 0.3em;
  /* Small padding for inline code */
  border-radius: 3px;
}

/* Ensure code inside pre doesn't get extra padding/background */
.textarea-styled-output pre code {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
}

/* Input and output styling */
textarea,
.textarea-styled-output {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.setting-group textarea,
.setting-group .textarea-styled-output {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  transition: all 0.3s ease;
}

.setting-group textarea:focus {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.35);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
}

/* Info section */
.info {
  margin-top: 20px;
  padding: 15px;
  background-color: var(--bg-light);
  border-radius: 8px;
  font-size: 0.85rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.info p {
  margin-bottom: 8px;
}

.info p:last-child {
  margin-bottom: 0;
}

.note {
  font-style: italic;
  font-size: 0.8rem;
  opacity: 0.8;
}

/* Cache Stats */
.cache-stats {
  margin-top: 15px;
  border-top: 1px solid var(--border-color-light);
  padding-top: 10px;
}

/* Warning and Error Messages */
.encryption-warning,
.error-message {
  margin-top: 10px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  line-height: 1.4;
}

.encryption-warning {
  background-color: rgba(255, 193, 7, 0.15);
  border-left: 3px solid #ffc107;
  color: #e6c046;
}

.error-message {
  background-color: rgba(220, 53, 69, 0.15);
  border-left: 3px solid #dc3545;
  color: #e05c6e;
}

.cache-stats details {
  font-size: 0.8rem;
}

.cache-stats summary {
  cursor: pointer;
  color: var(--color-primary);
  font-weight: 500;
}

.cache-stats .stats-content {
  background-color: rgba(10, 25, 41, 0.5);
  padding: 10px;
  border-radius: 6px;
  margin-top: 5px;
}

.cache-stats p {
  margin-bottom: 5px;
  font-size: 0.75rem;
}

#clear-cache {
  margin-top: 8px;
  background-color: var(--color-accent-1);
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

#clear-cache:hover {
  background-color: #8a0fd0;
}

/* Animation and feedback classes */
.bg-saved {
  background-color: rgba(10, 25, 41, 0.06) !important;
}

.bg-original {
  background-color: rgba(10, 25, 41, 0.5) !important;
}

.flash-animation {
  animation: flash 0.5s ease-in-out !important;
}

.no-animation {
  animation: none !important;
}

/* Height classes for textareas */
.height-auto {
  height: auto !important;
}

.custom-height {
  height: var(--custom-height) !important;
}

/* Toast Notifications */
.toast {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(100px);
  padding: 10px 20px;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast.show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

.toast-success {
  background-color: var(--color-accent-2);
}

.toast-error {
  background-color: #e63946;
}

.toast-warning {
  background-color: #ffb703;
  color: #333;
}

.toast-info {
  background-color: var(--color-primary);
}

/* Cache Stats */
.cache-stats {
  margin-top: 10px;
  font-size: 0.85rem;
}

.cache-stats details {
  border-radius: 4px;
  overflow: hidden;
}

.cache-stats summary {
  padding: 0 12px;
  background-color: var(--bg-light);
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
}

.cache-stats summary::after {
  content: '▼';
  font-size: 0.7rem;
  transition: transform 0.2s;
}

.cache-stats details[open] summary::after {
  transform: rotate(180deg);
}

.cache-stats .stats-content {
  padding: 10px 12px;
  background-color: var(--bg-dark);
}

.cache-stats .stats-section {
  margin-bottom: 12px;
  padding-bottom: 10px;
}

.cache-stats .stats-section:last-of-type {
  border-bottom: none;
  margin-bottom: 8px;
}

.cache-stats h4 {
  margin: 0 0 6px 0;
  color: var(--text-primary);
  font-size: 0.85rem;
  font-weight: 600;
}

.cache-stats p {
  margin: 4px 0;
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.cache-stats .stats-actions {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}

.cache-stats button {
  width: 50%;
  padding: 4px 8px;
  font-size: 0.8rem;
  background-color: var(--color-accent-1);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cache-stats button:hover {
  background-color: #5a0791;
}

#run-cleanup {
  margin-top: 8px;
  background-color: var(--color-accent-2);
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

/* Translation History */
.history-button {
  position: absolute;
  top: 12px;
  right: 0px;
  /* Align with the left edge of settings group */
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  z-index: 10;
  padding: 0;
}

.history-button:hover {
  transform: scale(1.1);
}

.history-button svg {
  width: 18px;
  height: 18px;
  stroke: white;
  stroke-width: 2;
  transition: stroke 0.2s ease;
}

/* Paste & Translate Button */
.paste-translate-button {
  position: absolute;
  top: 12px;
  right: 32px;
  /* Position to the left of history button */
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  z-index: 10;
  padding: 0;
}

.paste-translate-button:hover {
  transform: scale(1.1);
}

.paste-translate-button svg {
  width: 18px;
  height: 18px;
  stroke: white;
  stroke-width: 2;
  transition: stroke 0.2s ease;
}

/* History panel styling and hover behavior */
.history-panel {
  position: absolute;
  top: 40px;
  right: 0px;
  width: 300px;
  max-height: 400px;
  background-color: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: var(--glass-border);
  border-radius: 12px;
  box-shadow: var(--shadow-md), var(--shadow-glow);
  padding: 15px;
  z-index: 100;
  overflow-y: auto;

  /* Transition for smooth appearance/disappearance */
  transition: opacity 0.3s, visibility 0s 0.3s;
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}

/* Show history panel on hover with a slight delay before hiding */
.history-button:hover+.history-panel,
.history-panel:hover {
  visibility: visible;
  opacity: 0.98;
  pointer-events: auto;
  transition: opacity 0.2s, visibility 0s;
}

/* Hover effect for history items */
.history-item {
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.history-item:hover {
  transform: translateX(2px);
}

/* Add a subtle transition for the history button */
.history-button svg {
  transition: transform 0.2s ease, stroke 0.2s ease;
}

.history-button:hover svg {
  transform: scale(1.1);
  stroke: rgba(255, 255, 255, 0.95);
}

.history-panel h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--text-primary);
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-panel .clear-history {
  font-size: 12px;
  background: none;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  padding: 2px 5px;
  border-radius: 4px;
}

.history-panel .clear-history:hover {
  text-decoration: underline;
  background-color: rgba(76, 201, 240, 0.1);
}

.history-item {
  padding: 8px 10px;
  border-bottom: 1px solid rgba(76, 201, 240, 0.1);
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 6px;
  margin-bottom: 5px;
}

.history-item:hover {
  background-color: rgba(76, 201, 240, 0.1);
}

.history-item:last-child {
  border-bottom: none;
}

.history-item .direction {
  font-size: 11px;
  color: var(--color-primary);
  margin-bottom: 3px;
  display: block;
}

.history-item .text {
  font-size: 13px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 3px;
}

.history-item .translation {
  font-size: 13px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item .timestamp {
  font-size: 10px;
  color: var(--text-secondary);
  text-align: right;
  margin-top: 3px;
}

.no-history {
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  padding: 20px 0;
  font-size: 13px;
}

/* Keyboard shortcut indicators */
.keyboard-shortcut {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.keyboard-shortcut kbd {
  background-color: rgba(76, 201, 240, 0.1);
  border: 1px solid rgba(76, 201, 240, 0.2);
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  color: var(--color-primary);
  font-family: 'Courier New', monospace;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 5px;
  margin: 0 2px;
  min-width: 20px;
  text-align: center;
}

.keyboard-shortcut span {
  color: var(--text-secondary);
  font-size: 12px;
  margin: 0 4px;
}

*:hover>.keyboard-shortcut {
  opacity: 1;
}

/* YouTube section styles - using standard styling */
#youtube-link {
  /* Standard styling from translate sections */
  border-color: rgba(76, 201, 240, 0.15);
  width: 100%;
  padding: 12px 14px;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: rgba(10, 25, 41, 0.5);
  resize: vertical;
  min-height: 70px;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.2px;
}

#youtube-link:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(76, 201, 240, 0.2), 0 0 8px rgba(76, 201, 240, 0.1);
  background-color: rgba(10, 25, 41, 0.7);
}

#youtube-link::placeholder {
  color: var(--text-placeholder);
  opacity: 0.8;
}

#youtube-summary {
  margin-top: 15px;
  padding: 15px;
  background-color: rgba(10, 25, 41, 0.3);
  border-radius: 10px;
  min-height: 100px;
}

/* Error message styles */
.error {
  display: block;
  padding: 12px 16px;
  margin: 10px 0;
  background-color: rgba(220, 53, 69, 0.15);
  border-left: 3px solid #dc3545;
  color: #e05c6e;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
}

/* Tooltip styles */
.tooltip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: rgba(76, 201, 240, 0.15);
  color: var(--color-primary);
  font-size: 12px;
  font-weight: bold;
  margin-left: 6px;
  cursor: help;
  transition: all 0.2s ease;
}

.tooltip-icon:hover {
  background-color: rgba(76, 201, 240, 0.3);
}

/* Checkbox label styles */
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-text {
  margin-left: 6px;
}

/* Position select group */
.position-select-group {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.position-select-group.active {
  opacity: 1;
}

/* Flex row space between */
.flex-row-space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}