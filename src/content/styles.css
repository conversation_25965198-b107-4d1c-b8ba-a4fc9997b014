/* ===========================
   Gemini Translator Extension
   Consolidated Stylesheet
   =========================== */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Baloo+2:wght@400..800&family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Cabin:ital,wght@0,400..700;1,400..700&family=Comfortaa:wght@300..700&family=Crimson+Pro:ital,wght@0,200..900;1,200..900&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Fraunces:ital,opsz,wght@0,9..144,100..900;1,9..144,100..900&family=Geologica:wght@100..900&family=Lexend+Deca:wght@100..900&family=Literata:ital,opsz,wght@0,7..72,200..900;1,7..72,200..900&family=Mali:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700&family=Manrope:wght@200..800&family=Maven+Pro:wght@400..900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Mulish:ital,wght@0,200..1000;1,200..1000&family=Newsreader:ital,opsz,wght@0,6..72,200..800;1,6..72,200..800&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Petrona:ital,wght@0,100..900;1,100..900&family=Questrial&family=Quicksand:wght@300..700&family=Raleway:ital,wght@0,100..900;1,100..900&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&family=Roboto:ital,wght@0,100..900;1,100..900&family=Space+Grotesk:wght@300..700&family=Varela+Round&family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap');

/* ===========================
   Base Reset
   =========================== */
.gemini-translator-button,
.gemini-translator-popup,
.gemini-translator-popup * {
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Source Sans Pro', 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  box-sizing: border-box !important;
}

/* ===========================
   Floating Translate Button
   =========================== */
.gemini-translator-button {
  position: absolute;
  width: 16px;
  height: 16px;
  padding: 2px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px) saturate(130%);
  -webkit-backdrop-filter: blur(4px) saturate(130%);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 1px solid rgba(209, 213, 219, 0.2);
  z-index: 10000;
}

.gemini-translator-button svg {
  width: 10px;
  height: 10px;
  stroke: #2563eb;
  stroke-width: 2;
}

.gemini-translator-button:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.gemini-translator-button:active {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Loading animation for button */
.gemini-translator-button.loading {
  animation: float 1.5s ease-in-out infinite;
  padding: 0 !important;
}

/* ===========================
   Popup Container
   =========================== */
.gemini-translator-popup,
#gemini-translator-popup {
  position: absolute !important;
  z-index: 10000 !important;
  max-width: 600px !important;
  min-width: 280px !important;
  width: auto !important;
  background: transparent !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0 !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  transform: none !important;
  filter: none !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* ===========================
   Popup Content
   =========================== */
.gemini-translator-content {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  color: #1a1a1a;
}

.gemini-translator-original {
  color: #4a5568;
  font-size: 15px;
  line-height: 1.6;
  padding: 14px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  border: 1px solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.gemini-translator-divider {
  height: 1px;
  background: rgba(209, 213, 219, 0.5);
  margin: 4px 0;
}

.gemini-translator-card {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 0.5rem;
  background-color: #18385a;
  padding: 6px;
  overflow: hidden;
  box-shadow: inset 0 0 30px rgba(255, 255, 255, 0.08), 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.gemini-translator-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 70%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.05), transparent);
  pointer-events: none;
  border-radius: 0.5rem 0.5rem 2rem 2rem;
  backdrop-filter: blur(1px);
  opacity: 0.8;
}

.gemini-translator-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.03), transparent);
  pointer-events: none;
  border-radius: 2rem 2rem 0.5rem 0.5rem;
}

.gemini-translator-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.gemini-translator-card-header h3 {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1;
  color: #94a3b8;
  margin: 0;
}

.gemini-translator-translation {
  color: #94a3b8;
  line-height: 1.6;
  font-size: 0.875rem;
  word-wrap: break-word;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  position: relative;
  overflow: hidden;
  padding: 4px;
  margin: 2px;
  background: rgba(33, 40, 48, 0.1);
  backdrop-filter: blur(8px);
  border-radius: 6px;
}

/* Typography inside translation */
.gemini-translator-translation h1,
.gemini-translator-translation h2,
.gemini-translator-translation h3 {
  margin: 8px 0;
  font-weight: 600;
  color: #94a3b8;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.gemini-translator-translation p {
  margin: 8px 0;
  color: rgba(255, 255, 255, 0.95);
}

.gemini-translator-translation code {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
  color: #e2e8f0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gemini-translator-translation pre {
  background: rgba(0, 0, 0, 0.2);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  color: #e2e8f0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gemini-translator-translation ul,
.gemini-translator-translation ol {
  padding-left: 20px;
  margin: 8px 0;
  color: rgba(255, 255, 255, 0.9);
}

.gemini-translator-translation strong {
  font-weight: 600;
  color: #94a3b8;
}

.gemini-translator-translation em {
  font-style: italic;
  color: rgba(255, 255, 255, 0.9);
}

/* Add light effect to translation box */
/* Remove special effects from translation text */
.gemini-translator-translation p {
  margin: 0;
  color: #94a3b8;
  font-size: 0.875rem;
}

/* Update typography for translation content */
.gemini-translator-translation h1,
.gemini-translator-translation h2,
.gemini-translator-translation h3 {
  margin: 8px 0;
  font-weight: 600;
  color: #94a3b8;
}


/* ===========================
   Close Button
   =========================== */
.gemini-translator-close {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.gemini-translator-error .gemini-translator-close {
  color: rgba(224, 92, 110, 0.7);
}

.gemini-translator-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.gemini-translator-error .gemini-translator-close:hover {
  background: rgba(220, 53, 69, 0.1);
  color: rgba(224, 92, 110, 0.9);
}

/* ===========================
   Loading State
   =========================== */
.gemini-translator-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.gemini-translator-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(26, 115, 232, 0.3);
  border-radius: 50%;
  border-top-color: #1a73e8;
  animation: gemini-translator-spin 1s ease-in-out infinite;
  margin-bottom: 12px;
}

.gemini-translator-loading p {
  color: #5f6368;
  font-size: 14px;
}

/* ===========================
   Error State
   =========================== */
.gemini-translator-error {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  overflow: hidden;
  border-left: 3px solid #dc3545;
}

.gemini-translator-error .gemini-translator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: rgba(220, 53, 69, 0.1);
  border-bottom: 1px solid rgba(220, 53, 69, 0.2);
}

.gemini-translator-error .gemini-translator-header span {
  color: #e05c6e;
  font-weight: 500;
  font-size: 14px;
}

.gemini-translator-message {
  padding: 12px 16px;
  color: #e05c6e;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 500;
}

/* ===========================
   Animations
   =========================== */
@keyframes float {

  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }

  25% {
    transform: translate(-25px, -20px) rotate(-15deg);
  }

  50% {
    transform: translate(20px, -35px) rotate(15deg);
  }

  75% {
    transform: translate(-15px, -25px) rotate(-10deg);
  }
}

@keyframes gemini-translator-fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
    filter: blur(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

/* Add animation to popup */
.gemini-translator-popup {
  animation: gemini-translator-fade-in 0.3s ease-out forwards;
}

@keyframes gemini-translator-spin {
  to {
    transform: rotate(360deg);
  }
}