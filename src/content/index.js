const popup = document.createElement('div');
popup.className = 'gemini-translator-popup';
popup.style.display = 'none';
document.body.appendChild(popup);

function initializeContentScript() {
  document.addEventListener('mouseup', handleSelectionEvent);
  document.addEventListener('keyup', handleSelectionEvent);

  chrome.runtime.onMessage.addListener((message) => {
    handleBackgroundMessages(message);
  });
}

function handleSelectionEvent(event) {
  const selection = window.getSelection();
  const selectedText = selection.toString().trim();

  if (!selectedText) {
    hidePopup();
    return;
  }

  let position;
  try {
    if (event && event.type === 'mouseup' && event.clientX != null && event.clientY != null) {
      position = {
        x: event.clientX + window.scrollX + 10,
        y: event.clientY + window.scrollY
      };
    } else if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      position = {
        x: rect.left + window.scrollX + (rect.width / 2),
        y: rect.bottom + window.scrollY
      };
    } else {
      hidePopup();
      return;
    }
  } catch (error) {
    console.error("Error getting selection position:", error);
    hidePopup();
    return;
  }


  showTranslationButton(position, selectedText);
}

function handleBackgroundMessages(message) {

  switch (message.action) {
    case 'showTranslation':
      showTranslationPopup(message.translation, message.position);
      break;
    case 'showError':
      showErrorPopup(message.error, message.position);
      break;
    case 'translateText':
      const selection = window.getSelection();
      if (selection.toString().trim() === message.text.trim()) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        const position = {
          x: rect.left + window.scrollX + (rect.width / 2),
          y: rect.bottom + window.scrollY
        };
        sendMessageToBackground({
          action: 'translate',
          text: message.text,

          position
        });
      }
      break;
    case 'updateFontStyle':
      if (popup.style.display === 'block') {
        updatePopupStyles();
      }
      break;
    case 'updateFontSize':
      if (popup.style.display === 'block') {
        updatePopupStyles();
      }
      break;
    default:

      break;
  }
}

function sendMessageToBackground(message) {
  chrome.runtime.sendMessage(message);
}

function showTranslationButton(position, text) {
  const loading = window.loading || {
    show: () => { },
    hide: () => { }
  };
  let button = document.getElementById('gemini-translator-button');

  if (!button) {
    button = document.createElement('button');
    button.id = 'gemini-translator-button';
    button.className = 'gemini-translator-button';
    document.body.appendChild(button);

    button.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M5 8l6 6"></path>
        <path d="M4 14l6-6 2-3"></path>
        <path d="M2 5h12"></path>
        <path d="M7 2h1"></path>
        <path d="M22 22l-5-10-5 10"></path>
        <path d="M14 18h6"></path>
      </svg>
    `;
  }

  button.style.left = `${position.x}px`;
  button.style.top = `${position.y}px`;
  button.style.display = 'flex';

  button.onclick = function () {
    if (window.showLoading) {
      window.showLoading(position);
    }

    sendMessageToBackground({
      action: 'translate',
      text: text,
      position: position
    });
    button.classList.add('loading');
  };
}

function formatAndSanitizeMarkdown(text) {
  if (!text || typeof text !== 'string') {
    return '<p>Translation not available.</p>';
  }

  try {
    marked.setOptions({
      sanitize: true,
      silent: true,
      headerIds: false,
      mangle: false
    });

    const rawHtml = marked.parse(text);
    const sanitizedHtml = DOMPurify.sanitize(rawHtml, {
      ALLOWED_TAGS: [
        // Basic formatting
        'p', 'br', 'strong', 'em', 'i', 'b', 'code', 'pre',
        // Lists
        'ul', 'ol', 'li',
        // Headings (limited to h3 and below for UI consistency)
        'h3', 'h4', 'h5', 'h6',
        // Other elements
        'blockquote', 'span'
      ],
      ALLOWED_ATTR: [],
      ADD_ATTR: ['class'],
      FORBID_TAGS: ['style', 'script', 'iframe', 'frame', 'object', 'embed', 'form', 'input', 'textarea', 'select', 'button'],
      FORBID_ATTR: ['style', 'onerror', 'onload', 'onclick', 'onmouseover'],
      ALLOW_DATA_ATTR: false,
      USE_PROFILES: { html: true },
      SANITIZE_DOM: true,
      KEEP_CONTENT: true,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_DOM_IMPORT: false,
      WHOLE_DOCUMENT: false,
      FORCE_BODY: true
    });

    return sanitizedHtml || '<p>Translation not available.</p>';
  } catch (error) {
    console.error('Error formatting markdown:', error);
    return '<p>Error formatting translation.</p>';
  }
}

function generatePopupStyles(fontFamily, fontSize) {
  const size = parseInt(fontSize, 10) || 15;
  const lineHeight = size > 20 ? 1.8 : 1.6;
  const letterSpacing = fontFamily && fontFamily.includes('Pacifico') ? '0.5px' : 'normal';

  return `
    @import url('https://fonts.googleapis.com/css2?family=Baloo+2:wght@400..800&family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Cabin:ital,wght@0,400..700;1,400..700&family=Comfortaa:wght@300..700&family=Crimson+Pro:ital,wght@0,200..900;1,200..900&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Fraunces:ital,opsz,wght@0,9..144,100..900;1,9..144,100..900&family=Geologica:wght@100..900&family=Lexend+Deca:wght@100..900&family=Literata:ital,opsz,wght@0,7..72,200..900;1,7..72,200..900&family=Mali:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700&family=Manrope:wght@200..800&family=Maven+Pro:wght@400..900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Mulish:ital,wght@0,200..1000;1,200..1000&family=Newsreader:ital,opsz,wght@0,6..72,200..800;1,6..72,200..800&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Petrona:ital,wght@0,100..900;1,100..900&family=Questrial&family=Quicksand:wght@300..700&family=Raleway:ital,wght@0,100..900;1,100..900&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&family=Roboto:ital,wght@0,100..900;1,100..900&family=Space+Grotesk:wght@300..700&family=Varela+Round&family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap');

    .gemini-translator-card {
      background-color: #0a1929 !important;
    }
    .gemini-translator-card-header h3 {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px!important;
      font-weight: 600 !important;
      line-height: 1 !important;
    }
    .gemini-translator-translation,
    .gemini-translator-translation p,
    .gemini-translator-translation h1,
    .gemini-translator-translation h2,
    .gemini-translator-translation h3,
    .gemini-translator-translation li,
    .gemini-translator-translation code,
    .gemini-translator-translation pre {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px!important;
      font-weight: 400!important;
      line-height: ${lineHeight} !important;
      letter-spacing: ${letterSpacing} !important;
      margin: 0 !important;
      color: #94a3b8 !important;
    }
    .gemini-translator-content {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px!important;
      gap: 0 !important;
      color: #94a3b8 !important;
    }
    .gemini-translator-translation {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px!important;
      padding: 1px 4px !important;
      color: #94a3b8 !important;
    }
    .gemini-translator-translation p {
      color: #94a3b8 !important;
    }
    .gemini-translator-translation pre {
      white-space: pre-wrap; /* Wrap long code lines */
      word-wrap: break-word;
      background-color: rgba(255, 255, 255, 0.05); /* Slight background for code blocks */
      padding: 5px;
      border-radius: 3px;
      color: #94a3b8 !important;
    }
    .gemini-translator-translation code {
       background-color: rgba(255, 255, 255, 0.05); /* Slight background for inline code */
       padding: 1px 3px;
       border-radius: 3px;
       color: #94a3b8 !important;
    }
  `;
}

function showTranslationPopup(translation, position, forcePosition = null) {
  const button = document.getElementById('gemini-translator-button');
  if (button) {
    button.classList.remove('loading');
    button.style.display = 'none';
  }

  // Ensure popup exists and is properly initialized
  let translationPopup = document.getElementById('gemini-translator-popup');
  if (!translationPopup) {
    translationPopup = document.createElement('div');
    translationPopup.id = 'gemini-translator-popup';
    translationPopup.className = 'gemini-translator-popup';

    // Apply critical styles directly to ensure cross-site compatibility
    Object.assign(translationPopup.style, {
      position: 'absolute',
      zIndex: '10000',
      maxWidth: '600px',
      minWidth: '280px',
      width: 'auto',
      background: 'transparent',
      border: 'none',
      borderRadius: '0.5rem',
      padding: '0',
      overflow: 'hidden',
      display: 'none',
      fontFamily: '-apple-system, BlinkMacSystemFont, system-ui, sans-serif',
      boxSizing: 'border-box'
    });

    document.body.appendChild(translationPopup);
  }

  const formattedTranslation = formatAndSanitizeMarkdown(translation);
  chrome.storage.local.get(['fontFamily', 'fontSize'], function (fontResult) {
    const fontFamily = fontResult.fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif';
    const fontSize = fontResult.fontSize || '15';

    const styles = generatePopupStyles(fontFamily, fontSize);

    translationPopup.innerHTML = `
      <div class="gemini-translator-content">
        <div class="gemini-translator-card">
          <div class="gemini-translator-translation">
            <style>${styles}</style>
            ${formattedTranslation}
          </div>
        </div>
      </div>
    `;

    // Use improved positioning similar to button
    positionPopupImproved(translationPopup, position, forcePosition);
    translationPopup.style.display = 'block';

    document.addEventListener('mousedown', closeOnClickOutside);
  });
}

function showErrorPopup(error, position) {
  // Ensure popup exists and is properly initialized
  let translationPopup = document.getElementById('gemini-translator-popup');
  if (!translationPopup) {
    translationPopup = document.createElement('div');
    translationPopup.id = 'gemini-translator-popup';
    translationPopup.className = 'gemini-translator-popup';

    // Apply critical styles directly to ensure cross-site compatibility
    Object.assign(translationPopup.style, {
      position: 'absolute',
      zIndex: '10000',
      maxWidth: '600px',
      minWidth: '280px',
      width: 'auto',
      background: 'transparent',
      border: 'none',
      borderRadius: '0.5rem',
      padding: '0',
      overflow: 'hidden',
      display: 'none',
      fontFamily: '-apple-system, BlinkMacSystemFont, system-ui, sans-serif',
      boxSizing: 'border-box'
    });

    document.body.appendChild(translationPopup);
  }

  translationPopup.innerHTML = `
    <div class="gemini-translator-content gemini-translator-error">
      <div class="gemini-translator-header">
        <span>Translation Error</span>
        <button class="gemini-translator-close">&times;</button>
      </div>
      <div class="gemini-translator-message">${error}</div>
    </div>
  `;

  const closeBtn = translationPopup.querySelector('.gemini-translator-close');
  if (closeBtn) {
    closeBtn.addEventListener('click', hidePopup);
  }

  positionPopupImproved(translationPopup, position);
  translationPopup.style.display = 'block';

  document.addEventListener('mousedown', closeOnClickOutside);
}

function updatePopupStyles() {
  chrome.storage.local.get(['fontFamily', 'fontSize'], function (fontResult) {
    const fontFamily = fontResult.fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif';
    const fontSize = fontResult.fontSize || '15';

    const styles = generatePopupStyles(fontFamily, fontSize);

    // Update both legacy popup and improved popup
    const popups = [
      popup,
      document.getElementById('gemini-translator-popup')
    ].filter(Boolean);

    popups.forEach(popupElement => {
      const styleElement = popupElement.querySelector('style');
      if (styleElement) {
        styleElement.textContent = styles;
      } else {
        const translationElement = popupElement.querySelector('.gemini-translator-translation');
        if (translationElement) {
          const newStyle = document.createElement('style');
          newStyle.textContent = styles;
          translationElement.prepend(newStyle);
        }
      }
    });
  });
}

function hidePopup() {
  // Hide the main popup (legacy)
  if (popup) {
    popup.style.display = 'none';
  }

  // Hide the improved popup
  const translationPopup = document.getElementById('gemini-translator-popup');
  if (translationPopup) {
    translationPopup.style.display = 'none';
  }

  document.removeEventListener('mousedown', closeOnClickOutside);

  const button = document.getElementById('gemini-translator-button');
  if (button) {
    button.style.display = 'none';
  }
}

function positionPopupImproved(popupElement, position, forcePosition = null) {
  chrome.storage.local.get(['autoPosition', 'defaultPosition'], function (prefs) {
    const autoPositionEnabled = typeof prefs.autoPosition === 'boolean' ? prefs.autoPosition : true;
    const defaultPositionValue = typeof prefs.defaultPosition === 'string' ? prefs.defaultPosition : 'below';

    // Apply critical positioning styles directly for cross-site compatibility
    Object.assign(popupElement.style, {
      position: 'absolute',
      left: '0px',
      top: '0px',
      zIndex: '10000'
    });

    // Use a setTimeout to ensure the popup has been rendered and has dimensions
    setTimeout(() => {
      // Get actual dimensions after rendering with fallbacks
      const popupRect = popupElement.getBoundingClientRect();
      const popupWidth = popupRect.width || popupElement.offsetWidth || 300;
      const popupHeight = popupRect.height || popupElement.offsetHeight || 150;

      // Get viewport dimensions
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const scrollX = window.scrollX || window.pageXOffset || 0;
      const scrollY = window.scrollY || window.pageYOffset || 0;

      let left = position.x - (popupWidth / 2);
      let top;

      // Handle forced position override
      if (forcePosition) {
        switch (forcePosition) {
          case 'above':
            top = position.y - popupHeight - 10;
            break;
          case 'below':
            top = position.y + 10;
            break;
          case 'cursor':
            top = position.y;
            break;
          default:
            top = position.y + 10;
        }
      } else if (autoPositionEnabled) {
        // Smart positioning - default below text
        top = position.y + 10;

        // Auto-adjust if it would go off-screen
        if (top + popupHeight > scrollY + viewportHeight - 10) {
          // Not enough room below, try above
          top = position.y - popupHeight - 10;

          // If still not enough room above, position at the top of viewport
          if (top < scrollY + 10) {
            top = scrollY + 10;
          }
        }
      } else {
        // Use the specified default position
        switch (defaultPositionValue) {
          case 'above':
            top = position.y - popupHeight - 10;
            break;
          case 'cursor':
            top = position.y;
            break;
          case 'below':
          default:
            top = position.y + 10;
            break;
        }

        // Safety checks to ensure popup stays within viewport
        if (top + popupHeight > scrollY + viewportHeight - 10) {
          top = scrollY + viewportHeight - popupHeight - 10;
        }
        if (top < scrollY + 10) {
          top = scrollY + 10;
        }
      }

      // Ensure popup stays within horizontal bounds
      const minLeft = scrollX + 10;
      const maxLeft = scrollX + viewportWidth - popupWidth - 10;

      if (left < minLeft) {
        left = minLeft;
      }
      if (left > maxLeft) {
        left = maxLeft;
      }

      // Apply the calculated position with important flags for cross-site compatibility
      Object.assign(popupElement.style, {
        left: `${Math.round(left)}px`,
        top: `${Math.round(top)}px`,
        position: 'absolute',
        zIndex: '10000'
      });
    }, 15); // Slightly longer timeout to ensure proper rendering
  });
}

// Keep the old function for backward compatibility
function positionPopup(position) {
  const popupElement = document.getElementById('gemini-translator-popup') || popup;
  positionPopupImproved(popupElement, position);
}

function closeOnClickOutside(event) {
  const translationPopup = document.getElementById('gemini-translator-popup');
  const isClickInsidePopup = (popup && popup.contains(event.target)) ||
    (translationPopup && translationPopup.contains(event.target));
  const isClickOnButton = event.target.id === 'gemini-translator-button' ||
    event.target.closest('#gemini-translator-button');

  if (!isClickInsidePopup && !isClickOnButton) {
    hidePopup();
  }
}

initializeContentScript();
